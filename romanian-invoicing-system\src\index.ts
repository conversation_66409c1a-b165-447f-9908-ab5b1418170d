import { Hono, type Context as Hono<PERSON>ontext, type Next } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';

import { InvoiceService } from './services/invoice.service';
import { CompanyService } from './services/company.service';
import { AuthService } from './services/auth.service';
import { StorageService } from './services/storage.service';
import { UblService } from './services/ubl.service';
import { AnafService } from './services/anaf.service';
import { EFacturaService } from './services/efactura.service';
import { PdfService } from './services/pdf.service';

// Import routes
import authRoutes from './routes/auth';
import invoiceRoutes from './routes/invoices';
import { companyRoutes } from './routes/companies';
import clientRoutes from './routes/clients';
import productRoutes from './routes/products';
import anafRoutes from './routes/anaf';

export interface Env {
    DB: D1Database;
    R2_BUCKET: R2Bucket;
    JWT_SECRET: string;
    ENCRYPTION_KEY: string; // For sensitive data encryption
    // Add other bindings like KV namespaces, secrets, etc.
    ANAF_CLIENT_ID_GLOBAL?: string; // Optional global ANAF client ID
    ANAF_CLIENT_SECRET_GLOBAL?: string; // Optional global ANAF client secret
    ANAF_TEST_MODE?: string; // "true" or "false"
    BROWSER: Fetcher; // For Puppeteer
}

// Define User interface
export interface User {
    id: number;
    email: string;
    company_id?: number; // company_id can be optional
    // Add other user properties as needed, e.g., roles, permissions
}

export type AppEnv = {
    Bindings: Env,
    Variables: {
        authService: AuthService;
        storageService: StorageService;
        companyService: CompanyService;
        invoiceService: InvoiceService;
        ublService: UblService;
        anafService?: AnafService; // Corrected: AnafService from anaf.ts is an interface
        eFacturaService?: EFacturaService;
        pdfService: PdfService;
        user?: User; // User type is now defined
    }
}

const app = new Hono<AppEnv>();

// Middleware
app.use('*', cors());
app.use('*', secureHeaders());

// Initialize services and add to context
app.use('*', async (c, next) => {
    // Initialize basic services first
    c.set('authService', new AuthService(c.env.JWT_SECRET));
    c.set('storageService', new StorageService(c.env.R2_BUCKET, "R2_BUCKET" as string));
    c.set('companyService', new CompanyService(c.env.DB));
    c.set('ublService', new UblService());
    c.set('pdfService', new PdfService());

    // Initialize ANAF and EFactura services with default/placeholder values
    // These will be overridden per-request with company-specific credentials when needed
    const defaultAnafService = new AnafService(
        c.env.DB,
        0, // placeholder company ID
        c.env.ANAF_CLIENT_ID_GLOBAL || 'placeholder',
        c.env.ANAF_CLIENT_SECRET_GLOBAL || 'placeholder',
        c.env.ANAF_TEST_MODE === 'true'
    );
    c.set('anafService', defaultAnafService);

    const defaultEFacturaService = new EFacturaService(
        c.env.DB,
        defaultAnafService,
        c.get('ublService')!,
        0, // placeholder company ID
        c.env.ANAF_TEST_MODE === 'true'
    );
    c.set('eFacturaService', defaultEFacturaService);

    // Now initialize InvoiceService with all required dependencies
    c.set('invoiceService', new InvoiceService(
        c.env.DB,
        defaultEFacturaService,
        defaultAnafService,
        c.get('ublService')!
    ));

    await next();
});

// Authentication Middleware (example - apply to protected routes)
// This should run after service initialization
const authMiddleware = async (c: HonoContext<AppEnv>, next: Next) => {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return c.json({ error: 'Unauthorized: Missing or invalid token format.' }, 401);
    }
    const token = authHeader.substring(7);
    const authService = c.get('authService');
    const user = await authService.verifyToken(token);
    if (!user) {
        return c.json({ error: 'Unauthorized: Invalid or expired token.' }, 401);
    }
    c.set('user', user as any);
    await next();
};

// --- Routes --- 

app.get('/', (c) => {
  return c.text('Romanian Invoicing System API');
});

// Public routes (e.g., login, register)
app.route('/auth', authRoutes); 

// Protected routes - apply authMiddleware
// Note: Hono's routing means middleware applied here will cover all sub-routes of '/api'
const api = new Hono<AppEnv>();
api.use('*', authMiddleware); // Apply auth to all /api routes

api.route('/invoices', invoiceRoutes);
api.route('/company', companyRoutes);
api.route('/clients', clientRoutes);
api.route('/products', productRoutes);
api.route('/anaf', anafRoutes);

// Default route for API base
api.get('/', (c) => c.json({ message: 'Romanian Invoicing API' }));

app.route('/api', api); // Mount the protected API routes under /api

// --- Error Handling ---
app.onError((err, c) => {
  console.error('Global Error Handler:', err);
  // TODO: Add more sophisticated error logging (e.g., to a logging service)
  if (err instanceof Error) {
    return c.json({ error: 'Internal Server Error', message: err.message }, 500);
  }
  return c.json({ error: 'Internal Server Error', message: 'An unexpected error occurred.' }, 500);
});

app.notFound((c) => {
  return c.json({ error: 'Not Found', message: `The path ${c.req.url} was not found.` }, 404);
});

export default app;