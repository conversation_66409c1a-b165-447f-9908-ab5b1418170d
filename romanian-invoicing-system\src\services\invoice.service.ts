// Core service for managing invoices (CRUD operations, business logic)
import { D1Database } from '@cloudflare/workers-types';
import { EFacturaService, EFacturaUploadResponse } from './efactura.service'; // Corrected: Import class and response type
import { AnafService } from './anaf.service'; // Corrected: Import class
import { UblService } from './ubl.service'; // Corrected: Import class

// Define interfaces based on your schema.sql
// These are simplified; you'll need to match your schema.sql precisely.
export interface InvoiceClient {
  id?: number;
  company_id: number;
  name: string;
  cif: string; // Cod Identificare Fiscala (VAT ID)
  // ... other client fields from schema
  email?: string;
  address?: string;
}

export interface InvoiceProduct {
  id?: number;
  company_id: number;
  name: string;
  price: number; // Consider using a library for monetary values (e.g., Dinero.js) if precision is critical
  vat_rate: number; // e.g., 19 for 19%
  // ... other product fields
}

export interface InvoiceItem {
  id?: number;
  invoice_id?: number;
  product_id: number;
  description: string;
  quantity: number;
  unit_price: number;
  vat_rate: number;
  // Calculated fields (value, vat_value, total_value) should ideally be derived or stored carefully
  value?: number; // quantity * unit_price
  vat_value?: number; // value * (vat_rate / 100)
  total_value?: number; // value + vat_value
}

export interface Invoice {
  id?: number;
  company_id: number;
  client_id: number;
  series: string;
  number: string;
  issue_date: string; // ISO 8601 format (YYYY-MM-DD)
  due_date: string;   // ISO 8601 format (YYYY-MM-DD)
  currency: string;   // e.g., RON, EUR
  // ... many other fields from schema.sql
  subtotal?: number;
  vat_total?: number;
  total_amount: number;
  status: 'draft' | 'issued' | 'sent' | 'paid' | 'overdue' | 'cancelled' | 'efactura_pending' | 'efactura_sent' | 'efactura_error';
  efactura_submission_id?: string;
  efactura_status?: string;
  items: InvoiceItem[];
  // ... other fields like observations, payment_details etc.
}

export class InvoiceService { // Changed from interface to class
  private db: D1Database;
  private efacturaService: EFacturaService;
  private anafService: AnafService; // Added AnafService
  private ublService: UblService; // Added UblService

  constructor(db: D1Database, efacturaService: EFacturaService, anafService: AnafService, ublService: UblService) {
    this.db = db;
    this.efacturaService = efacturaService;
    this.anafService = anafService; // Initialize AnafService
    this.ublService = ublService; // Initialize UblService
  }

  // Overloaded method signatures to support both old and new calling patterns
  async createInvoice(invoiceData: Omit<Invoice, 'id' | 'items'>, itemsData?: Omit<InvoiceItem, 'id' | 'invoice_id'>[]): Promise<Invoice>;
  async createInvoice(fullInvoiceData: Omit<Invoice, 'id'>): Promise<Invoice>;
  async createInvoice(invoiceDataOrFull: Omit<Invoice, 'id' | 'items'> | Omit<Invoice, 'id'>, itemsData?: Omit<InvoiceItem, 'id' | 'invoice_id'>[]): Promise<Invoice> {
    console.log('Creating invoice:', invoiceDataOrFull);

    // Handle both calling patterns
    let invoiceData: Omit<Invoice, 'id' | 'items'>;
    let items: Omit<InvoiceItem, 'id' | 'invoice_id'>[];

    if ('items' in invoiceDataOrFull && invoiceDataOrFull.items) {
      // Called with full invoice data including items
      const { items: invoiceItems, ...restData } = invoiceDataOrFull as Omit<Invoice, 'id'>;
      invoiceData = restData;
      items = invoiceItems.map(item => {
        const { id, invoice_id, ...itemData } = item;
        return itemData;
      });
    } else {
      // Called with separate invoice data and items
      invoiceData = invoiceDataOrFull as Omit<Invoice, 'id' | 'items'>;
      items = itemsData || [];
    }

    // TODO: Implement database insertion for invoice and its items in a transaction
    // 1. Insert into 'invoices' table
    // 2. Get the generated invoice ID
    // 3. Insert into 'invoice_items' table using the invoice ID
    // 4. Calculate totals if not provided or for verification

    // Placeholder implementation
    const newInvoiceId = Math.floor(Math.random() * 10000);
    const newInvoice: Invoice = {
      ...invoiceData,
      id: newInvoiceId,
      status: invoiceData.status || 'draft',
      items: items.map((item, index) => ({
        ...item,
        id: Math.floor(Math.random() * 100000 + index),
        invoice_id: newInvoiceId,
        value: item.quantity * item.unit_price,
        vat_value: item.quantity * item.unit_price * (item.vat_rate / 100),
        total_value: item.quantity * item.unit_price * (1 + item.vat_rate / 100),
      })),
    };
    // Simulate DB save
    // await this.db.prepare("INSERT INTO invoices (...) VALUES (...)").bind(...).run();
    // for (const item of newInvoice.items) {
    //   await this.db.prepare("INSERT INTO invoice_items (...) VALUES (...)").bind(...).run();
    // }
    return newInvoice;
  }

  async getInvoiceById(invoiceId: number, companyId?: number): Promise<InvoiceWithDetails | null> {
    console.log(`Getting invoice by ID: ${invoiceId}${companyId ? ` for company ${companyId}` : ''}`);
    // TODO: Implement database query to fetch invoice and its items
    // For now, using a placeholder that works with or without companyId
    
    // Placeholder implementation
    if (invoiceId === 1) {
      return {
        id: 1,
        company_id: companyId || 1,
        client_id: 101,
        series: 'ABC',
        number: '1001',
        issue_date: '2023-10-01',
        due_date: '2023-10-31',
        currency: 'RON',
        total_amount: 1190,
        status: 'issued',
        items: [],
        client_name: 'Test Client',
        client_vat_number: 'RO12345678',
        client_address: 'Test Address',
        client_city: 'Bucharest',
        client_county: 'Bucharest',
        client_country_code: 'RO',
        series_name: 'ABC',
        series_number: '1001',
        efactura_submission_id: null,
        efactura_status: null,
        created_at: '2023-10-01T00:00:00Z',
        updated_at: '2023-10-01T00:00:00Z'
      };
    }
    return null;
  }

  // Overloaded updateInvoice to support different parameter orders
  async updateInvoice(invoiceId: number, updateData: Partial<Invoice>, companyId?: number): Promise<Invoice | null>;
  async updateInvoice(invoiceId: number, companyId: number, updateData: Partial<Invoice>): Promise<Invoice | null>;
  async updateInvoice(invoiceId: number, updateDataOrCompanyId: Partial<Invoice> | number, companyIdOrUpdateData?: number | Partial<Invoice>): Promise<Invoice | null> {
    let updateData: Partial<Invoice>;
    let companyId: number | undefined;

    if (typeof updateDataOrCompanyId === 'number') {
      // Called with (invoiceId, companyId, updateData)
      companyId = updateDataOrCompanyId;
      updateData = companyIdOrUpdateData as Partial<Invoice>;
    } else {
      // Called with (invoiceId, updateData, companyId?)
      updateData = updateDataOrCompanyId;
      companyId = companyIdOrUpdateData as number | undefined;
    }

    console.log(`Updating invoice ${invoiceId}${companyId ? ` for company ${companyId}` : ''}:`, updateData);
    // TODO: Implement database update for invoice and potentially its items

    // Placeholder
    const existingInvoice = await this.getInvoiceById(invoiceId, companyId);
    if (!existingInvoice) return null;
    const updatedInvoice = { ...existingInvoice, ...updateData } as Invoice;
    return updatedInvoice;
  }

  async getInvoiceWithDetails(invoiceId: number, companyId?: number): Promise<InvoiceWithDetails | null> {
    // This method is the same as getInvoiceById for now, but could be extended
    // to include additional details like company information, etc.
    return this.getInvoiceById(invoiceId, companyId);
  }

  async updateInvoiceEFaturaStatus(invoiceId: number, status: string, submissionId?: string, downloadId?: string): Promise<void> {
    console.log(`Updating eFactura status for invoice ${invoiceId}: ${status}`);

    const updateData: Partial<Invoice> = {
      efactura_status: status,
    };

    if (submissionId) {
      updateData.efactura_submission_id = submissionId;
    }

    // Update the invoice status based on eFactura status
    if (status === 'SUBMITTED') {
      updateData.status = 'efactura_sent';
    } else if (status.includes('ERROR') || status.includes('REJECTED')) {
      updateData.status = 'efactura_error';
    }

    await this.updateInvoice(invoiceId, updateData);
  }

  async generateUblXml(invoiceId: number): Promise<string | null> {
    const invoice = await this.getInvoiceById(invoiceId);
    if (!invoice) {
        console.error(`Invoice with ID ${invoiceId} not found.`);
        return null;
    }

    // Placeholder company data
    const company = {
        id: 1,
        name: 'My Company SRL',
        vat_number: 'RO12345678',
        registration_number: 'J40/123/2023',
        address: 'Str. Exemplu Nr. 1',
        city: 'Bucuresti',
        county: 'Bucuresti',
        postal_code: '010101',
        country_code: 'RO',
        email: '<EMAIL>',
        phone: '**********',
        iban: '************************',
        bank_name: 'My Bank',
        user_id: 'user123'
    };

    // Placeholder client data
    const client = {
        name: invoice.client_name || 'N/A Client',
        vat_number: invoice.client_vat_number || null,
        registration_number: null,
        address: invoice.client_address || 'N/A Address',
        city: invoice.client_city || 'N/A City',
        county: invoice.client_county || null,
        postal_code: null,
        country_code: invoice.client_country_code || 'RO',
        email: null,
        phone: null
    };

    // Fetch invoice items
    const itemsStatement = this.db.prepare('SELECT * FROM invoice_items WHERE invoice_id = ?');
    const itemsResult = await itemsStatement.bind(invoiceId).all<InvoiceItem>();
    const invoiceItems: InvoiceItem[] = itemsResult.results || [];

    const fullInvoiceForUbl = {
        ...invoice,
        items: invoiceItems,
        invoice_number: `${invoice.series_name}/${invoice.series_number}`,
    };

    return this.ublService.generateInvoiceUbl(fullInvoiceForUbl as any, company, client);
  }

  async deleteInvoice(invoiceId: number, companyId: number): Promise<boolean> {
    console.log(`Deleting invoice ${invoiceId} for company ${companyId}`);
    // TODO: Implement database deletion (or soft delete)
    // Ensure related items are also handled.
    // const result = await this.db.prepare('DELETE FROM invoices WHERE id = ? AND company_id = ?').bind(invoiceId, companyId).run();
    // return result.success && result.meta.changes > 0;

    // Placeholder
    return invoiceId === 1; // Assume deletion is successful for ID 1
  }

  async listInvoices(companyId: number, filters: any = {}): Promise<Invoice[]> {
    console.log(`Listing invoices for company ${companyId} with filters:`, filters);
    // TODO: Implement database query with filtering, pagination, sorting
    // Example: const results = await this.db.prepare('SELECT * FROM invoices WHERE company_id = ?').bind(companyId).all();
    // return results.results as Invoice[];

    // Placeholder
    const inv1 = await this.getInvoiceById(1, companyId);
    return inv1 ? [inv1 as Invoice] : [];
  }

  async submitToEFactura(invoiceId: number, companyId: number): Promise<any> {
      const company = {
          id: companyId,
          name: 'My Company SRL',
          vat_number: 'RO12345678',
          registration_number: 'J40/123/2023',
          address: 'Str. Exemplu Nr. 1',
          city: 'Bucuresti',
          county: 'Bucuresti',
          postal_code: '010101',
          country_code: 'RO',
          email: '<EMAIL>',
          phone: '**********',
          iban: '************************',
          bank_name: 'My Bank',
          user_id: 'user123'
      };

      const cif = company.vat_number;
      const isVatPayer = !!(company.vat_number && company.vat_number.startsWith('RO'));

      const invoice = await this.getInvoiceById(invoiceId, companyId);
      if (!invoice) {
          throw new Error('Invoice not found');
      }
      if (invoice.status === 'efactura_sent' || invoice.status === 'efactura_pending') {
          console.log(`Invoice ${invoiceId} already processed or pending with eFactura.`);
          return invoice;
      }

      const ublXml = await this.generateUblXml(invoiceId);
      if (!ublXml) {
          await this.updateInvoice(invoiceId, { efactura_status: 'UBL Generation Failed', status: 'efactura_error' }, companyId);
          throw new Error('Failed to generate UBL XML for e-Factura submission.');
      }

      try {
          await this.updateInvoice(invoiceId, { status: 'efactura_pending', efactura_status: 'Submitting to eFactura...' }, companyId);
          
          const submissionResult = await this.efacturaService.submitInvoice(ublXml, cif, isVatPayer);

          if ('error' in submissionResult && submissionResult.error) {
              await this.updateInvoice(invoiceId, { efactura_status: `Submission Error: ${submissionResult.error}`, status: 'efactura_error' }, companyId);
              throw new Error(`e-Factura submission failed: ${submissionResult.error}`);
          }

          // Type guard to ensure we're working with EFacturaUploadResponse
          const uploadResponse = submissionResult as EFacturaUploadResponse;
          
          const updatePayload: Partial<Invoice> = {
              efactura_submission_id: uploadResponse.index_incarcare,
              efactura_status: uploadResponse.errors && uploadResponse.errors.length > 0 ? `Error: ${JSON.stringify(uploadResponse.errors)}` : 'Uploaded, pending ANAF processing',
          };

          if (uploadResponse.index_incarcare && (!uploadResponse.errors || uploadResponse.errors.length === 0)) {
              updatePayload.status = 'efactura_sent';
          } else {
              updatePayload.status = 'efactura_error';
          }
          return await this.updateInvoice(invoiceId, updatePayload, companyId);

      } catch (error: any) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          await this.updateInvoice(invoiceId, { efactura_status: `Submission Exception: ${errorMessage}`, status: 'efactura_error' }, companyId);
          throw error;
      }
  }

  async checkEFacturaStatus(invoiceId: number, companyId: number): Promise<any> {
      const invoice = await this.getInvoiceById(invoiceId, companyId);
      if (!invoice || !invoice.efactura_submission_id) {
          throw new Error('Invoice not found or not submitted to eFactura yet.');
      }

      try {
          const statusResult = await this.efacturaService.checkInvoiceStatus(invoice.efactura_submission_id);

          let newStatus = invoice.status;
          let newEfacturaStatus = `ANAF State: ${statusResult.stare}. Details: ${statusResult.detalii || 'N/A'}`;

          if (statusResult.stare === 'ok') {
              newStatus = 'efactura_confirmed';
              newEfacturaStatus = `Successfully processed by ANAF. ID: ${statusResult.id_incarcare}`;
          } else if (statusResult.stare === 'eroare' || (statusResult.errors && statusResult.errors.length > 0)) {
              newStatus = 'efactura_error';
              newEfacturaStatus = `Error from ANAF: ${statusResult.stare}. ${statusResult.detalii || JSON.stringify(statusResult.errors)}`;
          } else if (statusResult.stare === 'asteptare') {
              newStatus = 'efactura_pending';
          }

          await this.updateInvoice(invoiceId, { status: newStatus, efactura_status: newEfacturaStatus }, companyId);
          return { current_status: newStatus, details: statusResult };

      } catch (error: any) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          await this.updateInvoice(invoiceId, { efactura_status: `Status Check Exception: ${errorMessage}` }, companyId);
          throw error;
      }
  }
}

// How you might instantiate and use it in a Cloudflare Worker:
// export default {
//   async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
//     const invoiceService = new InvoiceService(env.DB); // Assuming DB is your D1 binding
//     // ... route requests to service methods ...
//   }
// }


// Placeholder for a more detailed invoice type, perhaps including items, client details, etc.
export interface InvoiceWithDetails extends Invoice {
    client_name: string;
    client_vat_number: string;
    client_address: string;
    client_city: string;
    client_county: string;
    client_country_code: string;
    series_name: string;
    series_number: string;
    efactura_submission_id: string | null;
    efactura_status: string | null;
    created_at: string;
    updated_at: string;
    // Add company and client objects for compatibility with invoice routes
    company?: {
        id: number;
        name: string;
        cif: string;
        cui: string; // Romanian CUI (same as cif but different property name)
        address: string;
        city: string;
        county: string;
        country: string;
        email?: string;
        phone?: string;
        platitor_tva?: boolean; // VAT payer status
    };
    client?: {
        id: number;
        name: string;
        cif: string;
        address: string;
        city: string;
        county: string;
        country_code: string;
        email?: string;
        phone?: string;
    };
}